# LONI Infrastructure

This directory contains the Docker Compose configuration for orchestrating the complete LONI development environment.

## 🏗️ Architecture Overview

The infrastructure consists of four main services:

- **Frontend**: Next.js application with Bun package manager
- **Backend**: FastAPI application with Python/uv
- **PostgreSQL**: Primary database for application data
- **Qdrant**: Vector database for AI/ML operations

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose installed
- At least 4GB of available RAM
- Ports 3000, 8000, 5432, 6333, 6334 available

### Setup

1. **Copy environment configuration:**
   ```bash
   cd apps/infra
   cp .env.example .env
   ```

2. **Edit environment variables:**
   ```bash
   # Update .env with your preferred settings
   nano .env
   ```

3. **Start the development environment:**
   ```bash
   # Production build (default)
   docker-compose up -d

   # OR Development with hot reload
   docker-compose --profile dev up -d
   ```

4. **Access the applications:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - Backend Docs: http://localhost:8000/docs
   - Qdrant Dashboard: http://localhost:6333/dashboard

## 📋 Available Services

### Frontend Service
- **Container**: `loni-frontend`
- **Port**: 3000 (configurable via `FRONTEND_PORT`)
- **Technology**: Next.js with Bun
- **Features**: Production-optimized build, health checks
- **Hot Reload**: Use `frontend-dev` service with `--profile dev`

### Backend Service
- **Container**: `loni-backend`
- **Port**: 8000 (configurable via `BACKEND_PORT`)
- **Technology**: FastAPI with uv
- **Features**: Auto-reload, health checks, CORS configured
- **Dependencies**: PostgreSQL, Qdrant

### PostgreSQL Service
- **Container**: `loni-postgres`
- **Port**: 5432 (configurable via `POSTGRES_PORT`)
- **Version**: PostgreSQL 16 Alpine
- **Features**: Persistent storage, health checks, resource limits
- **Data**: Stored in `postgres_data` volume

### Qdrant Service
- **Container**: `loni-qdrant`
- **Ports**: 6333 (HTTP), 6334 (gRPC)
- **Version**: Qdrant v1.11.5
- **Features**: Persistent storage, health checks, resource limits
- **Data**: Stored in `qdrant_data` volume

## 🔧 Configuration

### Environment Variables

Key environment variables (see `.env.example` for complete list):

```bash
# Database
POSTGRES_DB=loni_db
POSTGRES_USER=loni_user
POSTGRES_PASSWORD=your_password

# API
BACKEND_PORT=8000
FRONTEND_PORT=3000

# Security
SECRET_KEY=your-secret-key
```

### Development vs Production

**Development Mode:**
```bash
# Use development profile for hot reload
docker-compose --profile dev up -d
```

**Production Mode:**
```bash
# Use default profile for optimized builds
docker-compose up -d
```

## 🛠️ Common Commands

### Service Management
```bash
# Start all services
docker-compose up -d

# Start specific service
docker-compose up -d postgres

# Stop all services
docker-compose down

# Restart a service
docker-compose restart backend

# View logs
docker-compose logs -f backend
```

### Development Workflow
```bash
# Rebuild and restart after code changes
docker-compose up -d --build

# Reset database (WARNING: destroys data)
docker-compose down -v
docker-compose up -d

# Access service shell
docker-compose exec backend bash
docker-compose exec postgres psql -U loni_user -d loni_db
```

### Monitoring
```bash
# View service status
docker-compose ps

# Monitor resource usage
docker stats

# Check service health
docker-compose exec backend curl http://localhost:8000/health
```

## 🔍 Troubleshooting

### Common Issues

**Port Conflicts:**
```bash
# Check what's using the port
lsof -i :3000
# Update port in .env file
```

**Database Connection Issues:**
```bash
# Check PostgreSQL logs
docker-compose logs postgres

# Verify database is ready
docker-compose exec postgres pg_isready -U loni_user
```

**Frontend Build Issues:**
```bash
# Clear Next.js cache
docker-compose exec frontend rm -rf .next
docker-compose restart frontend
```

**Memory Issues:**
```bash
# Check available memory
free -h

# Adjust resource limits in docker-compose.yml
```

### Health Checks

All services include health checks. Check status:
```bash
docker-compose ps
```

Healthy services show `Up (healthy)` status.

## 📊 Resource Requirements

### Minimum Requirements
- **RAM**: 4GB
- **CPU**: 2 cores
- **Disk**: 10GB free space

### Recommended for Development
- **RAM**: 8GB
- **CPU**: 4 cores
- **Disk**: 20GB free space

## 🔒 Security Notes

### Development
- Default passwords are used (change in `.env`)
- Debug mode enabled
- CORS allows localhost origins

### Production
- Use strong passwords
- Disable debug mode
- Configure proper CORS origins
- Use HTTPS
- Consider using Docker secrets for sensitive data

## 📝 Maintenance

### Backup
```bash
# Backup PostgreSQL
docker-compose exec postgres pg_dump -U loni_user loni_db > backup.sql

# Backup Qdrant (copy volume)
docker run --rm -v loni_qdrant_data:/data -v $(pwd):/backup alpine tar czf /backup/qdrant_backup.tar.gz -C /data .
```

### Updates
```bash
# Update images
docker-compose pull

# Rebuild with latest code
docker-compose up -d --build
```

## 🤝 Contributing

When modifying the infrastructure:

1. Test changes locally
2. Update documentation
3. Verify all health checks pass
4. Test both development and production profiles
