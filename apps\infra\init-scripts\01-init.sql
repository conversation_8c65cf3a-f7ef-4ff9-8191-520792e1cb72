-- LONI Database Initialization Script
-- This script runs when PostgreSQL container starts for the first time

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create additional schemas if needed
-- CREATE SCHEMA IF NOT EXISTS app_schema;

-- Set default permissions
-- GRANT ALL PRIVILEGES ON DATABASE loni_db TO loni_user;

-- Log initialization
SELECT 'LONI database initialized successfully' AS status;
