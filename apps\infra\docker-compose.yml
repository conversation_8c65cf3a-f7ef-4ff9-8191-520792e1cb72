# Shared network for inter-service communication
networks:
  loni-network:
    driver: bridge

# Persistent volumes for data storage
volumes:
  postgres_data:
    driver: local
  qdrant_data:
    driver: local

services:
  # PostgreSQL Database Service
  postgres:
    image: postgres:16-alpine
    container_name: loni-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-loni_db}
      POSTGRES_USER: ${POSTGRES_USER:-loni_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-loni_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    networks:
      - loni-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-loni_user} -d ${POSTGRES_DB:-loni_db}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Qdrant Vector Database Service
  qdrant:
    image: qdrant/qdrant:v1.11.5
    container_name: loni-qdrant
    restart: unless-stopped
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
      QDRANT__LOG_LEVEL: INFO
      QDRANT__STORAGE__STORAGE_PATH: /qdrant/storage
    ports:
      - "${QDRANT_HTTP_PORT:-6333}:6333"
      - "${QDRANT_GRPC_PORT:-6334}:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    networks:
      - loni-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Backend API Service
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: loni-backend
    restart: unless-stopped
    environment:
      # Database configuration
      DATABASE_URL: postgresql://${POSTGRES_USER:-loni_user}:${POSTGRES_PASSWORD:-loni_password}@postgres:5432/${POSTGRES_DB:-loni_db}
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      POSTGRES_DB: ${POSTGRES_DB:-loni_db}
      POSTGRES_USER: ${POSTGRES_USER:-loni_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-loni_password}
      
      # Qdrant configuration
      QDRANT_HOST: qdrant
      QDRANT_HTTP_PORT: 6333
      QDRANT_GRPC_PORT: 6334
      QDRANT_URL: http://qdrant:6333
      
      # Application configuration
      ENVIRONMENT: ${ENVIRONMENT:-development}
      DEBUG: ${DEBUG:-true}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      API_V1_STR: ${API_V1_STR:-/api/v1}
      
      # Security
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-change-in-production}
      ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      
      # CORS
      BACKEND_CORS_ORIGINS: ${BACKEND_CORS_ORIGINS:-["http://localhost:3000","http://frontend:3000"]}
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    volumes:
      - ../backend:/app:ro
      - /app/.venv
    networks:
      - loni-network
    depends_on:
      postgres:
        condition: service_healthy
      qdrant:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Frontend Service
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
      target: ${FRONTEND_TARGET:-runner}
    container_name: loni-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      NEXT_TELEMETRY_DISABLED: 1
      PORT: 3000
      HOSTNAME: "0.0.0.0"
      
      # API configuration
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:8000}
      NEXT_PUBLIC_API_V1_STR: ${NEXT_PUBLIC_API_V1_STR:-/api/v1}
      
      # Internal API URL for server-side requests
      API_URL: http://backend:8000
      
      # Feature flags
      NEXT_PUBLIC_ENABLE_ANALYTICS: ${NEXT_PUBLIC_ENABLE_ANALYTICS:-false}
      NEXT_PUBLIC_ENABLE_DEBUG: ${NEXT_PUBLIC_ENABLE_DEBUG:-true}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    volumes:
      # For development hot reload (comment out for production)
      - ../frontend:/app:ro
      - /app/node_modules
      - /app/.next
    networks:
      - loni-network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Development Frontend Service (Alternative for hot reload)
  frontend-dev:
    build:
      context: ../frontend
      dockerfile: Dockerfile
      target: base
    container_name: loni-frontend-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      NEXT_TELEMETRY_DISABLED: 1
      PORT: 3000
      HOSTNAME: "0.0.0.0"
      NEXT_PUBLIC_API_URL: http://localhost:8000
      API_URL: http://backend:8000
      WATCHPACK_POLLING: true
      CHOKIDAR_USEPOLLING: true
    ports:
      - "${FRONTEND_DEV_PORT:-3001}:3000"
    volumes:
      - ../frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - loni-network
    depends_on:
      backend:
        condition: service_healthy
    command: ["bun", "run", "dev"]
    profiles:
      - dev
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
