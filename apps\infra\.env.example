# =============================================================================
# LONI Development Environment Configuration
# =============================================================================
# Copy this file to .env and update the values as needed
# This file contains all environment variables used by docker-compose.yml

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL)
# =============================================================================
POSTGRES_DB=loni_db
POSTGRES_USER=loni_user
POSTGRES_PASSWORD=loni_password_change_in_production
POSTGRES_PORT=5432

# =============================================================================
# VECTOR DATABASE CONFIGURATION (Qdrant)
# =============================================================================
QDRANT_HTTP_PORT=6333
QDRANT_GRPC_PORT=6334

# =============================================================================
# BACKEND CONFIGURATION (FastAPI)
# =============================================================================
BACKEND_PORT=8000
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=info
API_V1_STR=/api/v1

# Security Configuration
SECRET_KEY=your-super-secret-key-change-in-production-min-32-chars
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration (JSON array format)
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:3001","http://frontend:3000"]

# =============================================================================
# FRONTEND CONFIGURATION (Next.js)
# =============================================================================
FRONTEND_PORT=3000
FRONTEND_DEV_PORT=3001
NODE_ENV=development

# Build target for frontend (runner for production, base for development)
FRONTEND_TARGET=runner

# API URLs
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_V1_STR=/api/v1

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_DEBUG=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Set to true to enable development mode with hot reload
DEV_MODE=true

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================
# Uncomment and modify these for production deployment

# ENVIRONMENT=production
# DEBUG=false
# NODE_ENV=production
# FRONTEND_TARGET=runner
# NEXT_PUBLIC_ENABLE_DEBUG=false
# NEXT_PUBLIC_ENABLE_ANALYTICS=true

# Production Database (use strong passwords)
# POSTGRES_PASSWORD=your-very-strong-production-password
# SECRET_KEY=your-production-secret-key-at-least-32-characters-long

# Production API URLs (update with your domain)
# NEXT_PUBLIC_API_URL=https://api.yourdomain.com
# BACKEND_CORS_ORIGINS=["https://yourdomain.com","https://www.yourdomain.com"]

# =============================================================================
# DOCKER COMPOSE PROFILES
# =============================================================================
# Available profiles:
# - default: Runs backend, postgres, qdrant (no frontend)
# - prod: Runs frontend (production build), backend, postgres, qdrant
# - dev: Runs frontend-dev (with hot reload), backend, postgres, qdrant
#
# Usage:
# docker-compose up                         # Default profile (backend only)
# docker-compose --profile prod up          # Production profile
# docker-compose --profile dev up           # Development profile with hot reload
# =============================================================================
